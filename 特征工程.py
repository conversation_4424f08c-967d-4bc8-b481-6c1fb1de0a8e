import pandas as pd
import numpy as np
from datetime import datetime
import re
import os

def clean_cell_data(value):
    """清除单元格中的空格和不可见字符"""
    if pd.isna(value) or value is None:
        return value
    return re.sub(r'\s+', '', str(value).strip())

def standardize_date_format(date_str):
    """统一日期格式为 YYYY-MM-DD"""
    if pd.isna(date_str) or date_str in ['', 'nan', 'NaT']:
        return np.nan
    
    # 清除日期中的不可见字符
    date_str = clean_cell_data(date_str)
    if not date_str:
        return np.nan
    
    # 处理各种日期格式
    try:
        # 处理 YYYYMMDD 格式
        if re.match(r'^\d{8}$', date_str):
            return datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d')
        
        # 处理 YYYY/M/D 或 YYYY/MM/DD 格式
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) >= 3:
                year = parts[0]
                month = parts[1].zfill(2)
                day = parts[2].zfill(2)
                return f"{year}-{month}-{day}"
            elif len(parts) == 2:
                year = parts[0]
                month = parts[1].zfill(2)
                return f"{year}-{month}-01"  # 默认设置日期为1号
            else:
                return np.nan
        
        # 处理 YYYY-M-D 格式
        if re.match(r'^\d{4}-\d{1,2}-\d{1,2}$', date_str):
            parts = date_str.split('-')
            if len(parts) == 3:
                year = parts[0]
                month = parts[1].zfill(2)
                day = parts[2].zfill(2)
                return f"{year}-{month}-{day}"
        
        # 处理 YYYY-MM-DD 格式
        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return date_str
            
    except Exception as e:
        print(f"日期格式化错误: {date_str}, 错误: {str(e)}")
    
    return np.nan

def calculate_gestational_weeks(lmp_date, test_date):
    """计算检测孕周并覆盖原有值"""
    try:
        # 验证日期有效性
        if pd.isna(lmp_date) or pd.isna(test_date):
            return np.nan, False
            
        # 转换为日期对象
        lmp_datetime = datetime.strptime(str(lmp_date), '%Y-%m-%d')
        test_datetime = datetime.strptime(str(test_date), '%Y-%m-%d')
        
        # 计算日期差
        date_diff = (test_datetime - lmp_datetime).days
        if date_diff < 0:
            return np.nan, False
            
        # 转换成孕周（浮点数）
        weeks = round(date_diff / 7, 2)
        return weeks, True
    except Exception as e:
        return np.nan, False

def clean_and_process_data(df, data_type):
    """清洗数据并重新计算孕周"""
    print(f"\n处理{data_type}数据")
    
    # 清洗所有单元格数据
    print("清除单元格中的空格和不可见字符...")
    for col in df.columns:
        df[col] = df[col].apply(clean_cell_data)
    
    # 统一日期格式
    print("统一日期格式...")
    if '末次月经' in df.columns:
        df['末次月经'] = df['末次月经'].apply(standardize_date_format)
    if '检测日期' in df.columns:
        df['检测日期'] = df['检测日期'].apply(standardize_date_format)
    
    # 重新计算孕周
    print("重新计算检测孕周...")
    failed_records = []
    total_count = len(df)
    success_count = 0
    
    # 创建孕周计算状态列
    df['孕周计算状态'] = '失败'
    
    # 确保检测孕周列存在
    if '检测孕周' not in df.columns:
        df['检测孕周'] = np.nan
    
    for index, row in df.iterrows():
        patient_code = row.get('孕妇代码', f"未知_{index}")
        lmp_date = row.get('末次月经', np.nan)
        test_date = row.get('检测日期', np.nan)
        
        # 计算孕周
        weeks, success = calculate_gestational_weeks(lmp_date, test_date)
        
        if success:
            # 直接覆盖原有检测孕周列
            df.at[index, '检测孕周'] = weeks
            df.at[index, '孕周计算状态'] = '成功'
            success_count += 1
        else:
            failed_records.append({
                '孕妇代码': patient_code,
                '末次月经': lmp_date,
                '检测日期': test_date,
                '原始检测孕周': row.get('检测孕周', np.nan),
                '数据类型': data_type
            })
    
    # 输出统计信息
    print(f"✅ 成功计算: {success_count}/{total_count} 条")
    print(f"❌ 失败: {len(failed_records)} 条")
    
    return df, failed_records

def remove_patients_with_failed_calculations(df, failed_records):
    """删除所有包含无法计算检测孕周的样本数据"""
    print("\n删除无法计算检测孕周的样本数据...")
    
    # 获取所有计算失败的孕妇代码
    failed_patient_codes = set()
    for record in failed_records:
        failed_patient_codes.add(record['孕妇代码'])
    
    # 删除前的记录数
    before_count = len(df)
    
    # 删除所有计算失败的孕妇的所有记录
    if '孕妇代码' in df.columns and failed_patient_codes:
        df = df[~df['孕妇代码'].isin(failed_patient_codes)]
        
    # 删除后的记录数
    after_count = len(df)
    removed_count = before_count - after_count
    
    print(f"🗑️ 已删除 {len(failed_patient_codes)} 位孕妇的所有记录，共 {removed_count} 条数据")
    print(f"📊 剩余数据: {after_count} 条")
    
    return df

def main():
    """主处理函数"""
    print("开始处理孕妇数据 - 清除数据、统一格式、计算孕周")
    
    # 文件路径配置
    base_dir = "特征工程"
    male_input = os.path.join(base_dir, "男胎检测数据.csv")
    female_input = os.path.join(base_dir, "女胎检测数据.csv")
    merged_output = os.path.join(base_dir, "合并_男胎女胎检测数据.csv")
    failed_output = os.path.join(base_dir, "孕周计算失败记录.csv")
    
    # 确保输出目录存在
    os.makedirs(base_dir, exist_ok=True)
    
    all_failed = []
    merged_df = pd.DataFrame()
    
    try:
        # 读取数据
        print("\n" + "="*60)
        print("读取原始数据...")
        male_df = pd.read_csv(male_input, encoding='utf-8') if os.path.exists(male_input) else pd.DataFrame()
        female_df = pd.read_csv(female_input, encoding='utf-8') if os.path.exists(female_input) else pd.DataFrame()
        
        # 添加胎儿性别列
        print("添加胎儿性别列...")
        male_df['胎儿性别'] = '男'
        female_df['胎儿性别'] = '女'
        
        # 合并数据
        print("合并男胎女胎数据...")
        merged_df = pd.concat([male_df, female_df], ignore_index=True)
        
        # 处理合并后的数据
        print("\n" + "="*60)
        print("处理合并数据...")
        merged_df, failed_records = clean_and_process_data(merged_df, "合并数据")
        all_failed.extend(failed_records)
        
        # 删除无法计算检测孕周的样本数据
        print("\n" + "="*60)
        merged_df = remove_patients_with_failed_calculations(merged_df, all_failed)
        
        # 保存结果
        print("\n" + "="*60)
        print("保存处理结果...")
        merged_df.to_csv(merged_output, index=False, encoding='utf-8')
        print(f"💾 合并输出文件: {merged_output}，总行数: {len(merged_df)}")
        
        # 保存失败记录
        if all_failed:
            failed_df = pd.DataFrame(all_failed)
            failed_df.to_csv(failed_output, index=False, encoding='utf-8')
            print(f"💾 保存失败记录: {failed_output} ({len(all_failed)} 条)")
        
        print("\n处理完成!")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 尝试保存当前处理进度
        if not merged_df.empty:
            temp_output = os.path.join(base_dir, "临时_处理结果.csv")
            merged_df.to_csv(temp_output, index=False, encoding='utf-8')
            print(f"⚠️ 已保存临时处理结果: {temp_output}")

if __name__ == "__main__":
    main()